# Credit Card Fraud Detection App

A simple machine learning application to detect fraudulent credit card transactions.

## Setup

1. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Run the application:
   ```
   python app.py
   ```

## Usage

Send a POST request to `/predict` with transaction data:

```json
{
  "transaction_id": "tx123456",
  "amount": 250.0,
  "hour": 14,
  "day": 15,
  "month": 3
}
```

The API will return a prediction:

```json
{
  "is_fraud": false,
  "fraud_probability": 0.12,
  "transaction_id": "tx123456"
}
```

## Training Your Own Model

To train a custom model with your data:

1. Place your CSV data in the project directory
2. Modify `main.py` to use your data
3. Run `python main.py` to train and save the model