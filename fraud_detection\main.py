import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix

def load_data(filepath):
    """Load and prepare credit card transaction data"""
    df = pd.read_csv(filepath)
    return df

def preprocess_data(df):
    """Preprocess the data for model training"""
    # Handle missing values
    df.fillna(0, inplace=True)
    
    # Split features and target
    X = df.drop('is_fraud', axis=1)
    y = df['is_fraud']
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Scale features
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)
    X_test = scaler.transform(X_test)
    
    return X_train, X_test, y_train, y_test, scaler

def train_model(X_train, y_train):
    """Train a fraud detection model"""
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    return model

def evaluate_model(model, X_test, y_test):
    """Evaluate model performance"""
    y_pred = model.predict(X_test)
    print(confusion_matrix(y_test, y_pred))
    print(classification_report(y_test, y_pred))
    return y_pred

def predict_transaction(model, scaler, transaction_data):
    """Predict if a new transaction is fraudulent"""
    scaled_data = scaler.transform([transaction_data])
    prediction = model.predict(scaled_data)[0]
    probability = model.predict_proba(scaled_data)[0][1]
    return prediction, probability

if __name__ == "__main__":
    # Example usage
    print("Credit Card Fraud Detection System")
    # In a real app, you would load actual data
    # df = load_data("credit_card_transactions.csv")
