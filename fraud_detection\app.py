from flask import Flask, request, jsonify
import joblib
import numpy as np
import os

app = Flask(__name__)

# Load the model and scaler
model = None
scaler = None

@app.route('/predict', methods=['POST'])
def predict():
    global model, scaler
    
    # Get transaction data from request
    data = request.get_json()
    
    # Extract features (adjust these based on your model's expected features)
    features = [
        data.get('amount', 0),
        data.get('hour', 0),
        data.get('day', 0),
        data.get('month', 0),
        # Add more features as needed
    ]
    
    # Scale the features
    scaled_features = scaler.transform([features])
    
    # Make prediction
    prediction = model.predict(scaled_features)[0]
    probability = model.predict_proba(scaled_features)[0][1]
    
    return jsonify({
        'is_fraud': bool(prediction),
        'fraud_probability': float(probability),
        'transaction_id': data.get('transaction_id', 'unknown')
    })

@app.route('/health', methods=['GET'])
def health():
    return jsonify({'status': 'ok'})

def load_models():
    global model, scaler
    # In a real app, load your trained model and scaler
    # model = joblib.load('models/fraud_model.pkl')
    # scaler = joblib.load('models/scaler.pkl')
    
    # For demo purposes, we'll use dummy models
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.preprocessing import StandardScaler
    model = RandomForestClassifier(n_estimators=10)
    scaler = StandardScaler()
    
    # Dummy fit
    X = np.random.rand(100, 4)
    y = np.random.randint(0, 2, 100)
    scaler.fit(X)
    model.fit(X, y)

if __name__ == '__main__':
    load_models()
    app.run(debug=True, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))